{"version": 3, "file": "850.4ff5be1ac6f4d6958c7a.js?v=4ff5be1ac6f4d6958c7a", "mappings": ";;;;;AAAA;AACA,CAAC,KAA4D;AAC7D,CAAC,CAC0B;AAC3B,CAAC,sBAAsB;;AAEvB;AACA,oBAAoB,aAAa;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,gBAAgB;AAC/C,EAAE;;AAEF;AACA,+BAA+B;AAC/B,+BAA+B,cAAc;AAC7C,EAAE;AACF;;AAEA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA,kCAAkC,SAAS,mBAAmB,aAAa;AAC3E,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0DAA0D,mBAAmB,aAAa;AAC1F,EAAE;;AAEF;;AAEA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,wFAAwF;AACxF,kFAAkF;AAClF,yFAAyF;AACzF;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA,KAAK,YAAY;AACjB,qDAAqD;AACrD,gCAAgC;AAChC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA,wBAAwB;AACxB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,qDAAqD;;AAErD;AACA,sEAAsE;AACtE,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF,EAAE;;AAEF;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB;AACrB,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA,EAAE;AACF;AACA,EAAE;AACF,EAAE;;AAEF;AACA,kCAAkC;AAClC;AACA;;AAEA;;AAEA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sFAAsF,wBAAwB;AAC9G,qEAAqE;AACrE,gEAAgE;AAChE;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,gCAAgC;AAChC,6CAA6C;AAC7C;AACA;AACA;AACA;AACA,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B;AAC1B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;AAEF;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,EAAE;;AAEF;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;;AAEN;AACA;AACA,MAAM;;AAEN;AACA;AACA,MAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,OAAO;;AAEP;AACA,6BAA6B,SAAS;AACtC;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,6BAA6B,kBAAkB;AAC/C;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,YAAY;AACZ;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,+BAA+B,iBAAiB;AAChD;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,sCAAsC;AACtC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;;AAEA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;;AAEA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,WAAW;AACX;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA,wDAAwD,WAAW;AACnE;;AAEA;AACA;AACA,sDAAsD,MAAM;AAC5D;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,iEAAiE,KAAK,GAAG,OAAO;AAChF;;AAEA;AACA,wDAAwD,cAAc,OAAO,KAAK,GAAG,OAAO;AAC5F;;AAEA;AACA;AACA;AACA;;AAEA;AACA,yFAAyF;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,0CAA0C,iBAAiB;AAC3D;;AAEA;;AAEA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA,mCAAmC,UAAU;;AAE7C;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,WAAW;AACX;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,yBAAyB,kBAAkB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,+EAA+E,yCAAyC;;AAExH;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,sCAAsC,iBAAiB;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,wBAAwB;AACxB,WAAW;AACX;AACA;AACA;AACA,2BAA2B,mBAAmB;AAC9C,eAAe;AACf;AACA;AACA,2BAA2B,iDAAiD;AAC5E;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,wCAAwC,gBAAgB;AACxD;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,yBAAyB,kBAAkB;AAC3C;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA,CAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/json5/dist/index.js"], "sourcesContent": ["(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.JSON5 = factory());\n}(this, (function () { 'use strict';\n\n\tfunction createCommonjsModule(fn, module) {\n\t\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n\t}\n\n\tvar _global = createCommonjsModule(function (module) {\n\t// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\n\tvar global = module.exports = typeof window != 'undefined' && window.Math == Math\n\t  ? window : typeof self != 'undefined' && self.Math == Math ? self\n\t  // eslint-disable-next-line no-new-func\n\t  : Function('return this')();\n\tif (typeof __g == 'number') { __g = global; } // eslint-disable-line no-undef\n\t});\n\n\tvar _core = createCommonjsModule(function (module) {\n\tvar core = module.exports = { version: '2.6.5' };\n\tif (typeof __e == 'number') { __e = core; } // eslint-disable-line no-undef\n\t});\n\tvar _core_1 = _core.version;\n\n\tvar _isObject = function (it) {\n\t  return typeof it === 'object' ? it !== null : typeof it === 'function';\n\t};\n\n\tvar _anObject = function (it) {\n\t  if (!_isObject(it)) { throw TypeError(it + ' is not an object!'); }\n\t  return it;\n\t};\n\n\tvar _fails = function (exec) {\n\t  try {\n\t    return !!exec();\n\t  } catch (e) {\n\t    return true;\n\t  }\n\t};\n\n\t// Thank's IE8 for his funny defineProperty\n\tvar _descriptors = !_fails(function () {\n\t  return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;\n\t});\n\n\tvar document = _global.document;\n\t// typeof document.createElement is 'object' in old IE\n\tvar is = _isObject(document) && _isObject(document.createElement);\n\tvar _domCreate = function (it) {\n\t  return is ? document.createElement(it) : {};\n\t};\n\n\tvar _ie8DomDefine = !_descriptors && !_fails(function () {\n\t  return Object.defineProperty(_domCreate('div'), 'a', { get: function () { return 7; } }).a != 7;\n\t});\n\n\t// 7.1.1 ToPrimitive(input [, PreferredType])\n\n\t// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n\t// and the second argument - flag - preferred type is a string\n\tvar _toPrimitive = function (it, S) {\n\t  if (!_isObject(it)) { return it; }\n\t  var fn, val;\n\t  if (S && typeof (fn = it.toString) == 'function' && !_isObject(val = fn.call(it))) { return val; }\n\t  if (typeof (fn = it.valueOf) == 'function' && !_isObject(val = fn.call(it))) { return val; }\n\t  if (!S && typeof (fn = it.toString) == 'function' && !_isObject(val = fn.call(it))) { return val; }\n\t  throw TypeError(\"Can't convert object to primitive value\");\n\t};\n\n\tvar dP = Object.defineProperty;\n\n\tvar f = _descriptors ? Object.defineProperty : function defineProperty(O, P, Attributes) {\n\t  _anObject(O);\n\t  P = _toPrimitive(P, true);\n\t  _anObject(Attributes);\n\t  if (_ie8DomDefine) { try {\n\t    return dP(O, P, Attributes);\n\t  } catch (e) { /* empty */ } }\n\t  if ('get' in Attributes || 'set' in Attributes) { throw TypeError('Accessors not supported!'); }\n\t  if ('value' in Attributes) { O[P] = Attributes.value; }\n\t  return O;\n\t};\n\n\tvar _objectDp = {\n\t\tf: f\n\t};\n\n\tvar _propertyDesc = function (bitmap, value) {\n\t  return {\n\t    enumerable: !(bitmap & 1),\n\t    configurable: !(bitmap & 2),\n\t    writable: !(bitmap & 4),\n\t    value: value\n\t  };\n\t};\n\n\tvar _hide = _descriptors ? function (object, key, value) {\n\t  return _objectDp.f(object, key, _propertyDesc(1, value));\n\t} : function (object, key, value) {\n\t  object[key] = value;\n\t  return object;\n\t};\n\n\tvar hasOwnProperty = {}.hasOwnProperty;\n\tvar _has = function (it, key) {\n\t  return hasOwnProperty.call(it, key);\n\t};\n\n\tvar id = 0;\n\tvar px = Math.random();\n\tvar _uid = function (key) {\n\t  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));\n\t};\n\n\tvar _library = false;\n\n\tvar _shared = createCommonjsModule(function (module) {\n\tvar SHARED = '__core-js_shared__';\n\tvar store = _global[SHARED] || (_global[SHARED] = {});\n\n\t(module.exports = function (key, value) {\n\t  return store[key] || (store[key] = value !== undefined ? value : {});\n\t})('versions', []).push({\n\t  version: _core.version,\n\t  mode: _library ? 'pure' : 'global',\n\t  copyright: '© 2019 Denis Pushkarev (zloirock.ru)'\n\t});\n\t});\n\n\tvar _functionToString = _shared('native-function-to-string', Function.toString);\n\n\tvar _redefine = createCommonjsModule(function (module) {\n\tvar SRC = _uid('src');\n\n\tvar TO_STRING = 'toString';\n\tvar TPL = ('' + _functionToString).split(TO_STRING);\n\n\t_core.inspectSource = function (it) {\n\t  return _functionToString.call(it);\n\t};\n\n\t(module.exports = function (O, key, val, safe) {\n\t  var isFunction = typeof val == 'function';\n\t  if (isFunction) { _has(val, 'name') || _hide(val, 'name', key); }\n\t  if (O[key] === val) { return; }\n\t  if (isFunction) { _has(val, SRC) || _hide(val, SRC, O[key] ? '' + O[key] : TPL.join(String(key))); }\n\t  if (O === _global) {\n\t    O[key] = val;\n\t  } else if (!safe) {\n\t    delete O[key];\n\t    _hide(O, key, val);\n\t  } else if (O[key]) {\n\t    O[key] = val;\n\t  } else {\n\t    _hide(O, key, val);\n\t  }\n\t// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n\t})(Function.prototype, TO_STRING, function toString() {\n\t  return typeof this == 'function' && this[SRC] || _functionToString.call(this);\n\t});\n\t});\n\n\tvar _aFunction = function (it) {\n\t  if (typeof it != 'function') { throw TypeError(it + ' is not a function!'); }\n\t  return it;\n\t};\n\n\t// optional / simple context binding\n\n\tvar _ctx = function (fn, that, length) {\n\t  _aFunction(fn);\n\t  if (that === undefined) { return fn; }\n\t  switch (length) {\n\t    case 1: return function (a) {\n\t      return fn.call(that, a);\n\t    };\n\t    case 2: return function (a, b) {\n\t      return fn.call(that, a, b);\n\t    };\n\t    case 3: return function (a, b, c) {\n\t      return fn.call(that, a, b, c);\n\t    };\n\t  }\n\t  return function (/* ...args */) {\n\t    return fn.apply(that, arguments);\n\t  };\n\t};\n\n\tvar PROTOTYPE = 'prototype';\n\n\tvar $export = function (type, name, source) {\n\t  var IS_FORCED = type & $export.F;\n\t  var IS_GLOBAL = type & $export.G;\n\t  var IS_STATIC = type & $export.S;\n\t  var IS_PROTO = type & $export.P;\n\t  var IS_BIND = type & $export.B;\n\t  var target = IS_GLOBAL ? _global : IS_STATIC ? _global[name] || (_global[name] = {}) : (_global[name] || {})[PROTOTYPE];\n\t  var exports = IS_GLOBAL ? _core : _core[name] || (_core[name] = {});\n\t  var expProto = exports[PROTOTYPE] || (exports[PROTOTYPE] = {});\n\t  var key, own, out, exp;\n\t  if (IS_GLOBAL) { source = name; }\n\t  for (key in source) {\n\t    // contains in native\n\t    own = !IS_FORCED && target && target[key] !== undefined;\n\t    // export native or passed\n\t    out = (own ? target : source)[key];\n\t    // bind timers to global for call from export context\n\t    exp = IS_BIND && own ? _ctx(out, _global) : IS_PROTO && typeof out == 'function' ? _ctx(Function.call, out) : out;\n\t    // extend global\n\t    if (target) { _redefine(target, key, out, type & $export.U); }\n\t    // export\n\t    if (exports[key] != out) { _hide(exports, key, exp); }\n\t    if (IS_PROTO && expProto[key] != out) { expProto[key] = out; }\n\t  }\n\t};\n\t_global.core = _core;\n\t// type bitmap\n\t$export.F = 1;   // forced\n\t$export.G = 2;   // global\n\t$export.S = 4;   // static\n\t$export.P = 8;   // proto\n\t$export.B = 16;  // bind\n\t$export.W = 32;  // wrap\n\t$export.U = 64;  // safe\n\t$export.R = 128; // real proto method for `library`\n\tvar _export = $export;\n\n\t// 7.1.4 ToInteger\n\tvar ceil = Math.ceil;\n\tvar floor = Math.floor;\n\tvar _toInteger = function (it) {\n\t  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);\n\t};\n\n\t// 7.2.1 RequireObjectCoercible(argument)\n\tvar _defined = function (it) {\n\t  if (it == undefined) { throw TypeError(\"Can't call method on  \" + it); }\n\t  return it;\n\t};\n\n\t// true  -> String#at\n\t// false -> String#codePointAt\n\tvar _stringAt = function (TO_STRING) {\n\t  return function (that, pos) {\n\t    var s = String(_defined(that));\n\t    var i = _toInteger(pos);\n\t    var l = s.length;\n\t    var a, b;\n\t    if (i < 0 || i >= l) { return TO_STRING ? '' : undefined; }\n\t    a = s.charCodeAt(i);\n\t    return a < 0xd800 || a > 0xdbff || i + 1 === l || (b = s.charCodeAt(i + 1)) < 0xdc00 || b > 0xdfff\n\t      ? TO_STRING ? s.charAt(i) : a\n\t      : TO_STRING ? s.slice(i, i + 2) : (a - 0xd800 << 10) + (b - 0xdc00) + 0x10000;\n\t  };\n\t};\n\n\tvar $at = _stringAt(false);\n\t_export(_export.P, 'String', {\n\t  // 21.1.3.3 String.prototype.codePointAt(pos)\n\t  codePointAt: function codePointAt(pos) {\n\t    return $at(this, pos);\n\t  }\n\t});\n\n\tvar codePointAt = _core.String.codePointAt;\n\n\tvar max = Math.max;\n\tvar min = Math.min;\n\tvar _toAbsoluteIndex = function (index, length) {\n\t  index = _toInteger(index);\n\t  return index < 0 ? max(index + length, 0) : min(index, length);\n\t};\n\n\tvar fromCharCode = String.fromCharCode;\n\tvar $fromCodePoint = String.fromCodePoint;\n\n\t// length should be 1, old FF problem\n\t_export(_export.S + _export.F * (!!$fromCodePoint && $fromCodePoint.length != 1), 'String', {\n\t  // 21.1.2.2 String.fromCodePoint(...codePoints)\n\t  fromCodePoint: function fromCodePoint(x) {\n\t    var arguments$1 = arguments;\n\t // eslint-disable-line no-unused-vars\n\t    var res = [];\n\t    var aLen = arguments.length;\n\t    var i = 0;\n\t    var code;\n\t    while (aLen > i) {\n\t      code = +arguments$1[i++];\n\t      if (_toAbsoluteIndex(code, 0x10ffff) !== code) { throw RangeError(code + ' is not a valid code point'); }\n\t      res.push(code < 0x10000\n\t        ? fromCharCode(code)\n\t        : fromCharCode(((code -= 0x10000) >> 10) + 0xd800, code % 0x400 + 0xdc00)\n\t      );\n\t    } return res.join('');\n\t  }\n\t});\n\n\tvar fromCodePoint = _core.String.fromCodePoint;\n\n\t// This is a generated file. Do not edit.\n\tvar Space_Separator = /[\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\n\tvar ID_Start = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF19]|\\uD806[\\uDCA0-\\uDCDF\\uDCFF\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE83\\uDE86-\\uDE89\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]/;\n\tvar ID_Continue = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u08D4-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u09FC\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9-\\u0AFF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D00-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1CD0-\\u1CD2\\u1CD4-\\u1CF9\\u1D00-\\u1DF9\\u1DFB-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC6F\\uDC7F-\\uDCBA\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDCA-\\uDDCC\\uDDD0-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3C-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB7\\uDEC0-\\uDEC9\\uDF00-\\uDF19\\uDF1D-\\uDF2B\\uDF30-\\uDF39]|\\uD806[\\uDCA0-\\uDCE9\\uDCFF\\uDE00-\\uDE3E\\uDE47\\uDE50-\\uDE83\\uDE86-\\uDE99\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD36\\uDD3A\\uDD3C\\uDD3D\\uDD3F-\\uDD47\\uDD50-\\uDD59]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50-\\uDF7E\\uDF8F-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4A\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uDB40[\\uDD00-\\uDDEF]/;\n\n\tvar unicode = {\n\t\tSpace_Separator: Space_Separator,\n\t\tID_Start: ID_Start,\n\t\tID_Continue: ID_Continue\n\t};\n\n\tvar util = {\n\t    isSpaceSeparator: function isSpaceSeparator (c) {\n\t        return typeof c === 'string' && unicode.Space_Separator.test(c)\n\t    },\n\n\t    isIdStartChar: function isIdStartChar (c) {\n\t        return typeof c === 'string' && (\n\t            (c >= 'a' && c <= 'z') ||\n\t        (c >= 'A' && c <= 'Z') ||\n\t        (c === '$') || (c === '_') ||\n\t        unicode.ID_Start.test(c)\n\t        )\n\t    },\n\n\t    isIdContinueChar: function isIdContinueChar (c) {\n\t        return typeof c === 'string' && (\n\t            (c >= 'a' && c <= 'z') ||\n\t        (c >= 'A' && c <= 'Z') ||\n\t        (c >= '0' && c <= '9') ||\n\t        (c === '$') || (c === '_') ||\n\t        (c === '\\u200C') || (c === '\\u200D') ||\n\t        unicode.ID_Continue.test(c)\n\t        )\n\t    },\n\n\t    isDigit: function isDigit (c) {\n\t        return typeof c === 'string' && /[0-9]/.test(c)\n\t    },\n\n\t    isHexDigit: function isHexDigit (c) {\n\t        return typeof c === 'string' && /[0-9A-Fa-f]/.test(c)\n\t    },\n\t};\n\n\tvar source;\n\tvar parseState;\n\tvar stack;\n\tvar pos;\n\tvar line;\n\tvar column;\n\tvar token;\n\tvar key;\n\tvar root;\n\n\tvar parse = function parse (text, reviver) {\n\t    source = String(text);\n\t    parseState = 'start';\n\t    stack = [];\n\t    pos = 0;\n\t    line = 1;\n\t    column = 0;\n\t    token = undefined;\n\t    key = undefined;\n\t    root = undefined;\n\n\t    do {\n\t        token = lex();\n\n\t        // This code is unreachable.\n\t        // if (!parseStates[parseState]) {\n\t        //     throw invalidParseState()\n\t        // }\n\n\t        parseStates[parseState]();\n\t    } while (token.type !== 'eof')\n\n\t    if (typeof reviver === 'function') {\n\t        return internalize({'': root}, '', reviver)\n\t    }\n\n\t    return root\n\t};\n\n\tfunction internalize (holder, name, reviver) {\n\t    var value = holder[name];\n\t    if (value != null && typeof value === 'object') {\n\t        if (Array.isArray(value)) {\n\t            for (var i = 0; i < value.length; i++) {\n\t                var key = String(i);\n\t                var replacement = internalize(value, key, reviver);\n\t                if (replacement === undefined) {\n\t                    delete value[key];\n\t                } else {\n\t                    Object.defineProperty(value, key, {\n\t                        value: replacement,\n\t                        writable: true,\n\t                        enumerable: true,\n\t                        configurable: true,\n\t                    });\n\t                }\n\t            }\n\t        } else {\n\t            for (var key$1 in value) {\n\t                var replacement$1 = internalize(value, key$1, reviver);\n\t                if (replacement$1 === undefined) {\n\t                    delete value[key$1];\n\t                } else {\n\t                    Object.defineProperty(value, key$1, {\n\t                        value: replacement$1,\n\t                        writable: true,\n\t                        enumerable: true,\n\t                        configurable: true,\n\t                    });\n\t                }\n\t            }\n\t        }\n\t    }\n\n\t    return reviver.call(holder, name, value)\n\t}\n\n\tvar lexState;\n\tvar buffer;\n\tvar doubleQuote;\n\tvar sign;\n\tvar c;\n\n\tfunction lex () {\n\t    lexState = 'default';\n\t    buffer = '';\n\t    doubleQuote = false;\n\t    sign = 1;\n\n\t    for (;;) {\n\t        c = peek();\n\n\t        // This code is unreachable.\n\t        // if (!lexStates[lexState]) {\n\t        //     throw invalidLexState(lexState)\n\t        // }\n\n\t        var token = lexStates[lexState]();\n\t        if (token) {\n\t            return token\n\t        }\n\t    }\n\t}\n\n\tfunction peek () {\n\t    if (source[pos]) {\n\t        return String.fromCodePoint(source.codePointAt(pos))\n\t    }\n\t}\n\n\tfunction read () {\n\t    var c = peek();\n\n\t    if (c === '\\n') {\n\t        line++;\n\t        column = 0;\n\t    } else if (c) {\n\t        column += c.length;\n\t    } else {\n\t        column++;\n\t    }\n\n\t    if (c) {\n\t        pos += c.length;\n\t    }\n\n\t    return c\n\t}\n\n\tvar lexStates = {\n\t    default: function default$1 () {\n\t        switch (c) {\n\t        case '\\t':\n\t        case '\\v':\n\t        case '\\f':\n\t        case ' ':\n\t        case '\\u00A0':\n\t        case '\\uFEFF':\n\t        case '\\n':\n\t        case '\\r':\n\t        case '\\u2028':\n\t        case '\\u2029':\n\t            read();\n\t            return\n\n\t        case '/':\n\t            read();\n\t            lexState = 'comment';\n\t            return\n\n\t        case undefined:\n\t            read();\n\t            return newToken('eof')\n\t        }\n\n\t        if (util.isSpaceSeparator(c)) {\n\t            read();\n\t            return\n\t        }\n\n\t        // This code is unreachable.\n\t        // if (!lexStates[parseState]) {\n\t        //     throw invalidLexState(parseState)\n\t        // }\n\n\t        return lexStates[parseState]()\n\t    },\n\n\t    comment: function comment () {\n\t        switch (c) {\n\t        case '*':\n\t            read();\n\t            lexState = 'multiLineComment';\n\t            return\n\n\t        case '/':\n\t            read();\n\t            lexState = 'singleLineComment';\n\t            return\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    multiLineComment: function multiLineComment () {\n\t        switch (c) {\n\t        case '*':\n\t            read();\n\t            lexState = 'multiLineCommentAsterisk';\n\t            return\n\n\t        case undefined:\n\t            throw invalidChar(read())\n\t        }\n\n\t        read();\n\t    },\n\n\t    multiLineCommentAsterisk: function multiLineCommentAsterisk () {\n\t        switch (c) {\n\t        case '*':\n\t            read();\n\t            return\n\n\t        case '/':\n\t            read();\n\t            lexState = 'default';\n\t            return\n\n\t        case undefined:\n\t            throw invalidChar(read())\n\t        }\n\n\t        read();\n\t        lexState = 'multiLineComment';\n\t    },\n\n\t    singleLineComment: function singleLineComment () {\n\t        switch (c) {\n\t        case '\\n':\n\t        case '\\r':\n\t        case '\\u2028':\n\t        case '\\u2029':\n\t            read();\n\t            lexState = 'default';\n\t            return\n\n\t        case undefined:\n\t            read();\n\t            return newToken('eof')\n\t        }\n\n\t        read();\n\t    },\n\n\t    value: function value () {\n\t        switch (c) {\n\t        case '{':\n\t        case '[':\n\t            return newToken('punctuator', read())\n\n\t        case 'n':\n\t            read();\n\t            literal('ull');\n\t            return newToken('null', null)\n\n\t        case 't':\n\t            read();\n\t            literal('rue');\n\t            return newToken('boolean', true)\n\n\t        case 'f':\n\t            read();\n\t            literal('alse');\n\t            return newToken('boolean', false)\n\n\t        case '-':\n\t        case '+':\n\t            if (read() === '-') {\n\t                sign = -1;\n\t            }\n\n\t            lexState = 'sign';\n\t            return\n\n\t        case '.':\n\t            buffer = read();\n\t            lexState = 'decimalPointLeading';\n\t            return\n\n\t        case '0':\n\t            buffer = read();\n\t            lexState = 'zero';\n\t            return\n\n\t        case '1':\n\t        case '2':\n\t        case '3':\n\t        case '4':\n\t        case '5':\n\t        case '6':\n\t        case '7':\n\t        case '8':\n\t        case '9':\n\t            buffer = read();\n\t            lexState = 'decimalInteger';\n\t            return\n\n\t        case 'I':\n\t            read();\n\t            literal('nfinity');\n\t            return newToken('numeric', Infinity)\n\n\t        case 'N':\n\t            read();\n\t            literal('aN');\n\t            return newToken('numeric', NaN)\n\n\t        case '\"':\n\t        case \"'\":\n\t            doubleQuote = (read() === '\"');\n\t            buffer = '';\n\t            lexState = 'string';\n\t            return\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    identifierNameStartEscape: function identifierNameStartEscape () {\n\t        if (c !== 'u') {\n\t            throw invalidChar(read())\n\t        }\n\n\t        read();\n\t        var u = unicodeEscape();\n\t        switch (u) {\n\t        case '$':\n\t        case '_':\n\t            break\n\n\t        default:\n\t            if (!util.isIdStartChar(u)) {\n\t                throw invalidIdentifier()\n\t            }\n\n\t            break\n\t        }\n\n\t        buffer += u;\n\t        lexState = 'identifierName';\n\t    },\n\n\t    identifierName: function identifierName () {\n\t        switch (c) {\n\t        case '$':\n\t        case '_':\n\t        case '\\u200C':\n\t        case '\\u200D':\n\t            buffer += read();\n\t            return\n\n\t        case '\\\\':\n\t            read();\n\t            lexState = 'identifierNameEscape';\n\t            return\n\t        }\n\n\t        if (util.isIdContinueChar(c)) {\n\t            buffer += read();\n\t            return\n\t        }\n\n\t        return newToken('identifier', buffer)\n\t    },\n\n\t    identifierNameEscape: function identifierNameEscape () {\n\t        if (c !== 'u') {\n\t            throw invalidChar(read())\n\t        }\n\n\t        read();\n\t        var u = unicodeEscape();\n\t        switch (u) {\n\t        case '$':\n\t        case '_':\n\t        case '\\u200C':\n\t        case '\\u200D':\n\t            break\n\n\t        default:\n\t            if (!util.isIdContinueChar(u)) {\n\t                throw invalidIdentifier()\n\t            }\n\n\t            break\n\t        }\n\n\t        buffer += u;\n\t        lexState = 'identifierName';\n\t    },\n\n\t    sign: function sign$1 () {\n\t        switch (c) {\n\t        case '.':\n\t            buffer = read();\n\t            lexState = 'decimalPointLeading';\n\t            return\n\n\t        case '0':\n\t            buffer = read();\n\t            lexState = 'zero';\n\t            return\n\n\t        case '1':\n\t        case '2':\n\t        case '3':\n\t        case '4':\n\t        case '5':\n\t        case '6':\n\t        case '7':\n\t        case '8':\n\t        case '9':\n\t            buffer = read();\n\t            lexState = 'decimalInteger';\n\t            return\n\n\t        case 'I':\n\t            read();\n\t            literal('nfinity');\n\t            return newToken('numeric', sign * Infinity)\n\n\t        case 'N':\n\t            read();\n\t            literal('aN');\n\t            return newToken('numeric', NaN)\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    zero: function zero () {\n\t        switch (c) {\n\t        case '.':\n\t            buffer += read();\n\t            lexState = 'decimalPoint';\n\t            return\n\n\t        case 'e':\n\t        case 'E':\n\t            buffer += read();\n\t            lexState = 'decimalExponent';\n\t            return\n\n\t        case 'x':\n\t        case 'X':\n\t            buffer += read();\n\t            lexState = 'hexadecimal';\n\t            return\n\t        }\n\n\t        return newToken('numeric', sign * 0)\n\t    },\n\n\t    decimalInteger: function decimalInteger () {\n\t        switch (c) {\n\t        case '.':\n\t            buffer += read();\n\t            lexState = 'decimalPoint';\n\t            return\n\n\t        case 'e':\n\t        case 'E':\n\t            buffer += read();\n\t            lexState = 'decimalExponent';\n\t            return\n\t        }\n\n\t        if (util.isDigit(c)) {\n\t            buffer += read();\n\t            return\n\t        }\n\n\t        return newToken('numeric', sign * Number(buffer))\n\t    },\n\n\t    decimalPointLeading: function decimalPointLeading () {\n\t        if (util.isDigit(c)) {\n\t            buffer += read();\n\t            lexState = 'decimalFraction';\n\t            return\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    decimalPoint: function decimalPoint () {\n\t        switch (c) {\n\t        case 'e':\n\t        case 'E':\n\t            buffer += read();\n\t            lexState = 'decimalExponent';\n\t            return\n\t        }\n\n\t        if (util.isDigit(c)) {\n\t            buffer += read();\n\t            lexState = 'decimalFraction';\n\t            return\n\t        }\n\n\t        return newToken('numeric', sign * Number(buffer))\n\t    },\n\n\t    decimalFraction: function decimalFraction () {\n\t        switch (c) {\n\t        case 'e':\n\t        case 'E':\n\t            buffer += read();\n\t            lexState = 'decimalExponent';\n\t            return\n\t        }\n\n\t        if (util.isDigit(c)) {\n\t            buffer += read();\n\t            return\n\t        }\n\n\t        return newToken('numeric', sign * Number(buffer))\n\t    },\n\n\t    decimalExponent: function decimalExponent () {\n\t        switch (c) {\n\t        case '+':\n\t        case '-':\n\t            buffer += read();\n\t            lexState = 'decimalExponentSign';\n\t            return\n\t        }\n\n\t        if (util.isDigit(c)) {\n\t            buffer += read();\n\t            lexState = 'decimalExponentInteger';\n\t            return\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    decimalExponentSign: function decimalExponentSign () {\n\t        if (util.isDigit(c)) {\n\t            buffer += read();\n\t            lexState = 'decimalExponentInteger';\n\t            return\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    decimalExponentInteger: function decimalExponentInteger () {\n\t        if (util.isDigit(c)) {\n\t            buffer += read();\n\t            return\n\t        }\n\n\t        return newToken('numeric', sign * Number(buffer))\n\t    },\n\n\t    hexadecimal: function hexadecimal () {\n\t        if (util.isHexDigit(c)) {\n\t            buffer += read();\n\t            lexState = 'hexadecimalInteger';\n\t            return\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    hexadecimalInteger: function hexadecimalInteger () {\n\t        if (util.isHexDigit(c)) {\n\t            buffer += read();\n\t            return\n\t        }\n\n\t        return newToken('numeric', sign * Number(buffer))\n\t    },\n\n\t    string: function string () {\n\t        switch (c) {\n\t        case '\\\\':\n\t            read();\n\t            buffer += escape();\n\t            return\n\n\t        case '\"':\n\t            if (doubleQuote) {\n\t                read();\n\t                return newToken('string', buffer)\n\t            }\n\n\t            buffer += read();\n\t            return\n\n\t        case \"'\":\n\t            if (!doubleQuote) {\n\t                read();\n\t                return newToken('string', buffer)\n\t            }\n\n\t            buffer += read();\n\t            return\n\n\t        case '\\n':\n\t        case '\\r':\n\t            throw invalidChar(read())\n\n\t        case '\\u2028':\n\t        case '\\u2029':\n\t            separatorChar(c);\n\t            break\n\n\t        case undefined:\n\t            throw invalidChar(read())\n\t        }\n\n\t        buffer += read();\n\t    },\n\n\t    start: function start () {\n\t        switch (c) {\n\t        case '{':\n\t        case '[':\n\t            return newToken('punctuator', read())\n\n\t        // This code is unreachable since the default lexState handles eof.\n\t        // case undefined:\n\t        //     return newToken('eof')\n\t        }\n\n\t        lexState = 'value';\n\t    },\n\n\t    beforePropertyName: function beforePropertyName () {\n\t        switch (c) {\n\t        case '$':\n\t        case '_':\n\t            buffer = read();\n\t            lexState = 'identifierName';\n\t            return\n\n\t        case '\\\\':\n\t            read();\n\t            lexState = 'identifierNameStartEscape';\n\t            return\n\n\t        case '}':\n\t            return newToken('punctuator', read())\n\n\t        case '\"':\n\t        case \"'\":\n\t            doubleQuote = (read() === '\"');\n\t            lexState = 'string';\n\t            return\n\t        }\n\n\t        if (util.isIdStartChar(c)) {\n\t            buffer += read();\n\t            lexState = 'identifierName';\n\t            return\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    afterPropertyName: function afterPropertyName () {\n\t        if (c === ':') {\n\t            return newToken('punctuator', read())\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    beforePropertyValue: function beforePropertyValue () {\n\t        lexState = 'value';\n\t    },\n\n\t    afterPropertyValue: function afterPropertyValue () {\n\t        switch (c) {\n\t        case ',':\n\t        case '}':\n\t            return newToken('punctuator', read())\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    beforeArrayValue: function beforeArrayValue () {\n\t        if (c === ']') {\n\t            return newToken('punctuator', read())\n\t        }\n\n\t        lexState = 'value';\n\t    },\n\n\t    afterArrayValue: function afterArrayValue () {\n\t        switch (c) {\n\t        case ',':\n\t        case ']':\n\t            return newToken('punctuator', read())\n\t        }\n\n\t        throw invalidChar(read())\n\t    },\n\n\t    end: function end () {\n\t        // This code is unreachable since it's handled by the default lexState.\n\t        // if (c === undefined) {\n\t        //     read()\n\t        //     return newToken('eof')\n\t        // }\n\n\t        throw invalidChar(read())\n\t    },\n\t};\n\n\tfunction newToken (type, value) {\n\t    return {\n\t        type: type,\n\t        value: value,\n\t        line: line,\n\t        column: column,\n\t    }\n\t}\n\n\tfunction literal (s) {\n\t    for (var i = 0, list = s; i < list.length; i += 1) {\n\t        var c = list[i];\n\n\t        var p = peek();\n\n\t        if (p !== c) {\n\t            throw invalidChar(read())\n\t        }\n\n\t        read();\n\t    }\n\t}\n\n\tfunction escape () {\n\t    var c = peek();\n\t    switch (c) {\n\t    case 'b':\n\t        read();\n\t        return '\\b'\n\n\t    case 'f':\n\t        read();\n\t        return '\\f'\n\n\t    case 'n':\n\t        read();\n\t        return '\\n'\n\n\t    case 'r':\n\t        read();\n\t        return '\\r'\n\n\t    case 't':\n\t        read();\n\t        return '\\t'\n\n\t    case 'v':\n\t        read();\n\t        return '\\v'\n\n\t    case '0':\n\t        read();\n\t        if (util.isDigit(peek())) {\n\t            throw invalidChar(read())\n\t        }\n\n\t        return '\\0'\n\n\t    case 'x':\n\t        read();\n\t        return hexEscape()\n\n\t    case 'u':\n\t        read();\n\t        return unicodeEscape()\n\n\t    case '\\n':\n\t    case '\\u2028':\n\t    case '\\u2029':\n\t        read();\n\t        return ''\n\n\t    case '\\r':\n\t        read();\n\t        if (peek() === '\\n') {\n\t            read();\n\t        }\n\n\t        return ''\n\n\t    case '1':\n\t    case '2':\n\t    case '3':\n\t    case '4':\n\t    case '5':\n\t    case '6':\n\t    case '7':\n\t    case '8':\n\t    case '9':\n\t        throw invalidChar(read())\n\n\t    case undefined:\n\t        throw invalidChar(read())\n\t    }\n\n\t    return read()\n\t}\n\n\tfunction hexEscape () {\n\t    var buffer = '';\n\t    var c = peek();\n\n\t    if (!util.isHexDigit(c)) {\n\t        throw invalidChar(read())\n\t    }\n\n\t    buffer += read();\n\n\t    c = peek();\n\t    if (!util.isHexDigit(c)) {\n\t        throw invalidChar(read())\n\t    }\n\n\t    buffer += read();\n\n\t    return String.fromCodePoint(parseInt(buffer, 16))\n\t}\n\n\tfunction unicodeEscape () {\n\t    var buffer = '';\n\t    var count = 4;\n\n\t    while (count-- > 0) {\n\t        var c = peek();\n\t        if (!util.isHexDigit(c)) {\n\t            throw invalidChar(read())\n\t        }\n\n\t        buffer += read();\n\t    }\n\n\t    return String.fromCodePoint(parseInt(buffer, 16))\n\t}\n\n\tvar parseStates = {\n\t    start: function start () {\n\t        if (token.type === 'eof') {\n\t            throw invalidEOF()\n\t        }\n\n\t        push();\n\t    },\n\n\t    beforePropertyName: function beforePropertyName () {\n\t        switch (token.type) {\n\t        case 'identifier':\n\t        case 'string':\n\t            key = token.value;\n\t            parseState = 'afterPropertyName';\n\t            return\n\n\t        case 'punctuator':\n\t            // This code is unreachable since it's handled by the lexState.\n\t            // if (token.value !== '}') {\n\t            //     throw invalidToken()\n\t            // }\n\n\t            pop();\n\t            return\n\n\t        case 'eof':\n\t            throw invalidEOF()\n\t        }\n\n\t        // This code is unreachable since it's handled by the lexState.\n\t        // throw invalidToken()\n\t    },\n\n\t    afterPropertyName: function afterPropertyName () {\n\t        // This code is unreachable since it's handled by the lexState.\n\t        // if (token.type !== 'punctuator' || token.value !== ':') {\n\t        //     throw invalidToken()\n\t        // }\n\n\t        if (token.type === 'eof') {\n\t            throw invalidEOF()\n\t        }\n\n\t        parseState = 'beforePropertyValue';\n\t    },\n\n\t    beforePropertyValue: function beforePropertyValue () {\n\t        if (token.type === 'eof') {\n\t            throw invalidEOF()\n\t        }\n\n\t        push();\n\t    },\n\n\t    beforeArrayValue: function beforeArrayValue () {\n\t        if (token.type === 'eof') {\n\t            throw invalidEOF()\n\t        }\n\n\t        if (token.type === 'punctuator' && token.value === ']') {\n\t            pop();\n\t            return\n\t        }\n\n\t        push();\n\t    },\n\n\t    afterPropertyValue: function afterPropertyValue () {\n\t        // This code is unreachable since it's handled by the lexState.\n\t        // if (token.type !== 'punctuator') {\n\t        //     throw invalidToken()\n\t        // }\n\n\t        if (token.type === 'eof') {\n\t            throw invalidEOF()\n\t        }\n\n\t        switch (token.value) {\n\t        case ',':\n\t            parseState = 'beforePropertyName';\n\t            return\n\n\t        case '}':\n\t            pop();\n\t        }\n\n\t        // This code is unreachable since it's handled by the lexState.\n\t        // throw invalidToken()\n\t    },\n\n\t    afterArrayValue: function afterArrayValue () {\n\t        // This code is unreachable since it's handled by the lexState.\n\t        // if (token.type !== 'punctuator') {\n\t        //     throw invalidToken()\n\t        // }\n\n\t        if (token.type === 'eof') {\n\t            throw invalidEOF()\n\t        }\n\n\t        switch (token.value) {\n\t        case ',':\n\t            parseState = 'beforeArrayValue';\n\t            return\n\n\t        case ']':\n\t            pop();\n\t        }\n\n\t        // This code is unreachable since it's handled by the lexState.\n\t        // throw invalidToken()\n\t    },\n\n\t    end: function end () {\n\t        // This code is unreachable since it's handled by the lexState.\n\t        // if (token.type !== 'eof') {\n\t        //     throw invalidToken()\n\t        // }\n\t    },\n\t};\n\n\tfunction push () {\n\t    var value;\n\n\t    switch (token.type) {\n\t    case 'punctuator':\n\t        switch (token.value) {\n\t        case '{':\n\t            value = {};\n\t            break\n\n\t        case '[':\n\t            value = [];\n\t            break\n\t        }\n\n\t        break\n\n\t    case 'null':\n\t    case 'boolean':\n\t    case 'numeric':\n\t    case 'string':\n\t        value = token.value;\n\t        break\n\n\t    // This code is unreachable.\n\t    // default:\n\t    //     throw invalidToken()\n\t    }\n\n\t    if (root === undefined) {\n\t        root = value;\n\t    } else {\n\t        var parent = stack[stack.length - 1];\n\t        if (Array.isArray(parent)) {\n\t            parent.push(value);\n\t        } else {\n\t            Object.defineProperty(parent, key, {\n\t                value: value,\n\t                writable: true,\n\t                enumerable: true,\n\t                configurable: true,\n\t            });\n\t        }\n\t    }\n\n\t    if (value !== null && typeof value === 'object') {\n\t        stack.push(value);\n\n\t        if (Array.isArray(value)) {\n\t            parseState = 'beforeArrayValue';\n\t        } else {\n\t            parseState = 'beforePropertyName';\n\t        }\n\t    } else {\n\t        var current = stack[stack.length - 1];\n\t        if (current == null) {\n\t            parseState = 'end';\n\t        } else if (Array.isArray(current)) {\n\t            parseState = 'afterArrayValue';\n\t        } else {\n\t            parseState = 'afterPropertyValue';\n\t        }\n\t    }\n\t}\n\n\tfunction pop () {\n\t    stack.pop();\n\n\t    var current = stack[stack.length - 1];\n\t    if (current == null) {\n\t        parseState = 'end';\n\t    } else if (Array.isArray(current)) {\n\t        parseState = 'afterArrayValue';\n\t    } else {\n\t        parseState = 'afterPropertyValue';\n\t    }\n\t}\n\n\t// This code is unreachable.\n\t// function invalidParseState () {\n\t//     return new Error(`JSON5: invalid parse state '${parseState}'`)\n\t// }\n\n\t// This code is unreachable.\n\t// function invalidLexState (state) {\n\t//     return new Error(`JSON5: invalid lex state '${state}'`)\n\t// }\n\n\tfunction invalidChar (c) {\n\t    if (c === undefined) {\n\t        return syntaxError((\"JSON5: invalid end of input at \" + line + \":\" + column))\n\t    }\n\n\t    return syntaxError((\"JSON5: invalid character '\" + (formatChar(c)) + \"' at \" + line + \":\" + column))\n\t}\n\n\tfunction invalidEOF () {\n\t    return syntaxError((\"JSON5: invalid end of input at \" + line + \":\" + column))\n\t}\n\n\t// This code is unreachable.\n\t// function invalidToken () {\n\t//     if (token.type === 'eof') {\n\t//         return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)\n\t//     }\n\n\t//     const c = String.fromCodePoint(token.value.codePointAt(0))\n\t//     return syntaxError(`JSON5: invalid character '${formatChar(c)}' at ${line}:${column}`)\n\t// }\n\n\tfunction invalidIdentifier () {\n\t    column -= 5;\n\t    return syntaxError((\"JSON5: invalid identifier character at \" + line + \":\" + column))\n\t}\n\n\tfunction separatorChar (c) {\n\t    console.warn((\"JSON5: '\" + (formatChar(c)) + \"' in strings is not valid ECMAScript; consider escaping\"));\n\t}\n\n\tfunction formatChar (c) {\n\t    var replacements = {\n\t        \"'\": \"\\\\'\",\n\t        '\"': '\\\\\"',\n\t        '\\\\': '\\\\\\\\',\n\t        '\\b': '\\\\b',\n\t        '\\f': '\\\\f',\n\t        '\\n': '\\\\n',\n\t        '\\r': '\\\\r',\n\t        '\\t': '\\\\t',\n\t        '\\v': '\\\\v',\n\t        '\\0': '\\\\0',\n\t        '\\u2028': '\\\\u2028',\n\t        '\\u2029': '\\\\u2029',\n\t    };\n\n\t    if (replacements[c]) {\n\t        return replacements[c]\n\t    }\n\n\t    if (c < ' ') {\n\t        var hexString = c.charCodeAt(0).toString(16);\n\t        return '\\\\x' + ('00' + hexString).substring(hexString.length)\n\t    }\n\n\t    return c\n\t}\n\n\tfunction syntaxError (message) {\n\t    var err = new SyntaxError(message);\n\t    err.lineNumber = line;\n\t    err.columnNumber = column;\n\t    return err\n\t}\n\n\tvar stringify = function stringify (value, replacer, space) {\n\t    var stack = [];\n\t    var indent = '';\n\t    var propertyList;\n\t    var replacerFunc;\n\t    var gap = '';\n\t    var quote;\n\n\t    if (\n\t        replacer != null &&\n\t        typeof replacer === 'object' &&\n\t        !Array.isArray(replacer)\n\t    ) {\n\t        space = replacer.space;\n\t        quote = replacer.quote;\n\t        replacer = replacer.replacer;\n\t    }\n\n\t    if (typeof replacer === 'function') {\n\t        replacerFunc = replacer;\n\t    } else if (Array.isArray(replacer)) {\n\t        propertyList = [];\n\t        for (var i = 0, list = replacer; i < list.length; i += 1) {\n\t            var v = list[i];\n\n\t            var item = (void 0);\n\n\t            if (typeof v === 'string') {\n\t                item = v;\n\t            } else if (\n\t                typeof v === 'number' ||\n\t                v instanceof String ||\n\t                v instanceof Number\n\t            ) {\n\t                item = String(v);\n\t            }\n\n\t            if (item !== undefined && propertyList.indexOf(item) < 0) {\n\t                propertyList.push(item);\n\t            }\n\t        }\n\t    }\n\n\t    if (space instanceof Number) {\n\t        space = Number(space);\n\t    } else if (space instanceof String) {\n\t        space = String(space);\n\t    }\n\n\t    if (typeof space === 'number') {\n\t        if (space > 0) {\n\t            space = Math.min(10, Math.floor(space));\n\t            gap = '          '.substr(0, space);\n\t        }\n\t    } else if (typeof space === 'string') {\n\t        gap = space.substr(0, 10);\n\t    }\n\n\t    return serializeProperty('', {'': value})\n\n\t    function serializeProperty (key, holder) {\n\t        var value = holder[key];\n\t        if (value != null) {\n\t            if (typeof value.toJSON5 === 'function') {\n\t                value = value.toJSON5(key);\n\t            } else if (typeof value.toJSON === 'function') {\n\t                value = value.toJSON(key);\n\t            }\n\t        }\n\n\t        if (replacerFunc) {\n\t            value = replacerFunc.call(holder, key, value);\n\t        }\n\n\t        if (value instanceof Number) {\n\t            value = Number(value);\n\t        } else if (value instanceof String) {\n\t            value = String(value);\n\t        } else if (value instanceof Boolean) {\n\t            value = value.valueOf();\n\t        }\n\n\t        switch (value) {\n\t        case null: return 'null'\n\t        case true: return 'true'\n\t        case false: return 'false'\n\t        }\n\n\t        if (typeof value === 'string') {\n\t            return quoteString(value, false)\n\t        }\n\n\t        if (typeof value === 'number') {\n\t            return String(value)\n\t        }\n\n\t        if (typeof value === 'object') {\n\t            return Array.isArray(value) ? serializeArray(value) : serializeObject(value)\n\t        }\n\n\t        return undefined\n\t    }\n\n\t    function quoteString (value) {\n\t        var quotes = {\n\t            \"'\": 0.1,\n\t            '\"': 0.2,\n\t        };\n\n\t        var replacements = {\n\t            \"'\": \"\\\\'\",\n\t            '\"': '\\\\\"',\n\t            '\\\\': '\\\\\\\\',\n\t            '\\b': '\\\\b',\n\t            '\\f': '\\\\f',\n\t            '\\n': '\\\\n',\n\t            '\\r': '\\\\r',\n\t            '\\t': '\\\\t',\n\t            '\\v': '\\\\v',\n\t            '\\0': '\\\\0',\n\t            '\\u2028': '\\\\u2028',\n\t            '\\u2029': '\\\\u2029',\n\t        };\n\n\t        var product = '';\n\n\t        for (var i = 0; i < value.length; i++) {\n\t            var c = value[i];\n\t            switch (c) {\n\t            case \"'\":\n\t            case '\"':\n\t                quotes[c]++;\n\t                product += c;\n\t                continue\n\n\t            case '\\0':\n\t                if (util.isDigit(value[i + 1])) {\n\t                    product += '\\\\x00';\n\t                    continue\n\t                }\n\t            }\n\n\t            if (replacements[c]) {\n\t                product += replacements[c];\n\t                continue\n\t            }\n\n\t            if (c < ' ') {\n\t                var hexString = c.charCodeAt(0).toString(16);\n\t                product += '\\\\x' + ('00' + hexString).substring(hexString.length);\n\t                continue\n\t            }\n\n\t            product += c;\n\t        }\n\n\t        var quoteChar = quote || Object.keys(quotes).reduce(function (a, b) { return (quotes[a] < quotes[b]) ? a : b; });\n\n\t        product = product.replace(new RegExp(quoteChar, 'g'), replacements[quoteChar]);\n\n\t        return quoteChar + product + quoteChar\n\t    }\n\n\t    function serializeObject (value) {\n\t        if (stack.indexOf(value) >= 0) {\n\t            throw TypeError('Converting circular structure to JSON5')\n\t        }\n\n\t        stack.push(value);\n\n\t        var stepback = indent;\n\t        indent = indent + gap;\n\n\t        var keys = propertyList || Object.keys(value);\n\t        var partial = [];\n\t        for (var i = 0, list = keys; i < list.length; i += 1) {\n\t            var key = list[i];\n\n\t            var propertyString = serializeProperty(key, value);\n\t            if (propertyString !== undefined) {\n\t                var member = serializeKey(key) + ':';\n\t                if (gap !== '') {\n\t                    member += ' ';\n\t                }\n\t                member += propertyString;\n\t                partial.push(member);\n\t            }\n\t        }\n\n\t        var final;\n\t        if (partial.length === 0) {\n\t            final = '{}';\n\t        } else {\n\t            var properties;\n\t            if (gap === '') {\n\t                properties = partial.join(',');\n\t                final = '{' + properties + '}';\n\t            } else {\n\t                var separator = ',\\n' + indent;\n\t                properties = partial.join(separator);\n\t                final = '{\\n' + indent + properties + ',\\n' + stepback + '}';\n\t            }\n\t        }\n\n\t        stack.pop();\n\t        indent = stepback;\n\t        return final\n\t    }\n\n\t    function serializeKey (key) {\n\t        if (key.length === 0) {\n\t            return quoteString(key, true)\n\t        }\n\n\t        var firstChar = String.fromCodePoint(key.codePointAt(0));\n\t        if (!util.isIdStartChar(firstChar)) {\n\t            return quoteString(key, true)\n\t        }\n\n\t        for (var i = firstChar.length; i < key.length; i++) {\n\t            if (!util.isIdContinueChar(String.fromCodePoint(key.codePointAt(i)))) {\n\t                return quoteString(key, true)\n\t            }\n\t        }\n\n\t        return key\n\t    }\n\n\t    function serializeArray (value) {\n\t        if (stack.indexOf(value) >= 0) {\n\t            throw TypeError('Converting circular structure to JSON5')\n\t        }\n\n\t        stack.push(value);\n\n\t        var stepback = indent;\n\t        indent = indent + gap;\n\n\t        var partial = [];\n\t        for (var i = 0; i < value.length; i++) {\n\t            var propertyString = serializeProperty(String(i), value);\n\t            partial.push((propertyString !== undefined) ? propertyString : 'null');\n\t        }\n\n\t        var final;\n\t        if (partial.length === 0) {\n\t            final = '[]';\n\t        } else {\n\t            if (gap === '') {\n\t                var properties = partial.join(',');\n\t                final = '[' + properties + ']';\n\t            } else {\n\t                var separator = ',\\n' + indent;\n\t                var properties$1 = partial.join(separator);\n\t                final = '[\\n' + indent + properties$1 + ',\\n' + stepback + ']';\n\t            }\n\t        }\n\n\t        stack.pop();\n\t        indent = stepback;\n\t        return final\n\t    }\n\t};\n\n\tvar JSON5 = {\n\t    parse: parse,\n\t    stringify: stringify,\n\t};\n\n\tvar lib = JSON5;\n\n\tvar es5 = lib;\n\n\treturn es5;\n\n})));\n"], "names": [], "sourceRoot": ""}
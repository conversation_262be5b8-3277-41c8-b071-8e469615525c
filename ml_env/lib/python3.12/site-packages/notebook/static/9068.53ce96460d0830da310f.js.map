{"version": 3, "file": "9068.53ce96460d0830da310f.js?v=53ce96460d0830da310f", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACoD;AACM;AACnD,wBAAwB,iBAAO;AACtC;AACA,YAAY,uBAAa;AACzB,CAAC;;;ACTD;AACA;AAC8B;;;ACF9B;AACA;AACuB", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/ui-components/lib/icon/iconimports.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/ui-components/lib/icon/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/ui-components/lib/index.js"], "sourcesContent": ["/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\nimport { LabIcon } from '@jupyterlab/ui-components';\nimport jupyterSvgstr from '../../style/icons/jupyter.svg';\nexport const jupyterIcon = new LabIcon({\n    name: 'notebook-ui-components:jupyter',\n    svgstr: jupyterSvgstr,\n});\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nexport * from './iconimports';\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nexport * from './icon';\n"], "names": [], "sourceRoot": ""}
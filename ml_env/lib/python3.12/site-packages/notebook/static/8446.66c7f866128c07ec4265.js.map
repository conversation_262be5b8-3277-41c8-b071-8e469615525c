{"version": 3, "file": "8446.66c7f866128c07ec4265.js?v=66c7f866128c07ec4265", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,MAAM;AAC3B;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ,oCAAoC;AACpC,sBAAsB;AACtB;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/tcl.js"], "sourcesContent": ["function parseWords(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = parseWords(\"Tcl safe after append array auto_execok auto_import auto_load \" +\n                          \"auto_mkindex auto_mkindex_old auto_qualify auto_reset bgerror \" +\n                          \"binary break catch cd close concat continue dde eof encoding error \" +\n                          \"eval exec exit expr fblocked fconfigure fcopy file fileevent filename \" +\n                          \"filename flush for foreach format gets glob global history http if \" +\n                          \"incr info interp join lappend lindex linsert list llength load lrange \" +\n                          \"lreplace lsearch lset lsort memory msgcat namespace open package parray \" +\n                          \"pid pkg::create pkg_mkIndex proc puts pwd re_syntax read regex regexp \" +\n                          \"registry regsub rename resource return scan seek set socket source split \" +\n                          \"string subst switch tcl_endOfWord tcl_findLibrary tcl_startOfNextWord \" +\n                          \"tcl_wordBreakAfter tcl_startOfPreviousWord tcl_wordBreakBefore tcltest \" +\n                          \"tclvars tell time trace unknown unset update uplevel upvar variable \" +\n                          \"vwait\");\nvar functions = parseWords(\"if elseif else and not or eq ne in ni for foreach while switch\");\nvar isOperatorChar = /[+\\-*&%=<>!?^\\/\\|]/;\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\nfunction tokenBase(stream, state) {\n  var beforeParams = state.beforeParams;\n  state.beforeParams = false;\n  var ch = stream.next();\n  if ((ch == '\"' || ch == \"'\") && state.inParams) {\n    return chain(stream, state, tokenString(ch));\n  } else if (/[\\[\\]{}\\(\\),;\\.]/.test(ch)) {\n    if (ch == \"(\" && beforeParams) state.inParams = true;\n    else if (ch == \")\") state.inParams = false;\n    return null;\n  } else if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  } else if (ch == \"#\") {\n    if (stream.eat(\"*\"))\n      return chain(stream, state, tokenComment);\n    if (ch == \"#\" && stream.match(/ *\\[ *\\[/))\n      return chain(stream, state, tokenUnparsed);\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == '\"') {\n    stream.skipTo(/\"/);\n    return \"comment\";\n  } else if (ch == \"$\") {\n    stream.eatWhile(/[$_a-z0-9A-Z\\.{:]/);\n    stream.eatWhile(/}/);\n    state.beforeParams = true;\n    return \"builtin\";\n  } else if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"comment\";\n  } else {\n    stream.eatWhile(/[\\w\\$_{}\\xa1-\\uffff]/);\n    var word = stream.current().toLowerCase();\n    if (keywords && keywords.propertyIsEnumerable(word))\n      return \"keyword\";\n    if (functions && functions.propertyIsEnumerable(word)) {\n      state.beforeParams = true;\n      return \"keyword\";\n    }\n    return null;\n  }\n}\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\nfunction tokenUnparsed(stream, state) {\n  var maybeEnd = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd == 2) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    if (ch == \"]\")\n      maybeEnd++;\n    else if (ch != \" \")\n      maybeEnd = 0;\n  }\n  return \"meta\";\n}\nexport const tcl = {\n  name: \"tcl\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      beforeParams: false,\n      inParams: false\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "sourceRoot": ""}
{"version": 3, "file": "8285.8bade38c361d9af60b43.js?v=8bade38c361d9af60b43", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,GAAG,6BAA6B;AACnD,gBAAgB,mBAAO,CAAC,KAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,uBAAuB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,UAAU;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,yCAAyC,2BAA2B,wBAAwB,sDAAsD;AAClJ;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;;;;;;ACtHa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,6CAA6C,6CAA6C;AAC1F,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,eAAe;AAC5D;AACA;AACA,yFAAyF,UAAU;AACnG;AACA;AACA;AACA,wHAAwH,UAAU;AAClI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,QAAQ;AAChD;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;;;;;;AC7Ja;AACb;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,YAAY;AACZ,mBAAmB,mBAAO,CAAC,IAAuB;AAClD,uBAAuB,mBAAO,CAAC,KAAkB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,sDAAsD;AAChG,uCAAuC,wBAAwB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;AACD,YAAY;AACZ;;;;;;;ACpKa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc,GAAG,uBAAuB,GAAG,6BAA6B,GAAG,qBAAqB,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,cAAc,GAAG,YAAY,GAAG,YAAY,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,eAAe,GAAG,cAAc,GAAG,cAAc,GAAG,gBAAgB;AACnU,eAAe;AACf;AACA;AACA;AACA;AACA,gBAAgB;AAChB,cAAc;AACd,cAAc;AACd,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,2DAA2D,UAAU;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,iFAAiF,MAAM;AACvF;AACA,YAAY;AACZ;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,6CAA6C;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,UAAU;AACrE;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,kCAAkC,qCAAqC;AACvE;AACA;AACA,sBAAsB;AACtB;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,kCAAkC,oCAAoC;AACtE;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA,oEAAoE,gBAAgB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA,gFAAgF,mBAAmB;AACnG;AACA,2BAA2B;AAC3B;AACA,+EAA+E,qBAAqB,UAAU;AAC9G;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,0BAA0B;AAC1B;AACA;AACA,cAAc;AACd;;;;;;;AC9Pa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,UAAU,GAAG,iBAAiB,GAAG,UAAU,GAAG,eAAe,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,aAAa,GAAG,gBAAgB;AAC3J,gBAAgB;AAChB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,4BAA4B;AAC5B,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,wBAAwB;AACxB,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/ui/safe/SafeHandler.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/ui/safe/SafeMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/ui/safe/safe.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/Options.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/lengths.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SafeHandler = exports.SafeMathDocumentMixin = void 0;\nvar safe_js_1 = require(\"./safe.js\");\nfunction SafeMathDocumentMixin(BaseDocument) {\n    var _a;\n    return _a = (function (_super) {\n            __extends(class_1, _super);\n            function class_1() {\n                var e_1, _a;\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n                _this.safe = new _this.options.SafeClass(_this, _this.options.safeOptions);\n                var ProcessBits = _this.constructor.ProcessBits;\n                if (!ProcessBits.has('safe')) {\n                    ProcessBits.allocate('safe');\n                }\n                try {\n                    for (var _b = __values(_this.inputJax), _c = _b.next(); !_c.done; _c = _b.next()) {\n                        var jax = _c.value;\n                        if (jax.name.match(/MathML/)) {\n                            jax.mathml.filterAttribute = _this.safe.mmlAttribute.bind(_this.safe);\n                            jax.mathml.filterClassList = _this.safe.mmlClassList.bind(_this.safe);\n                        }\n                        else if (jax.name.match(/TeX/)) {\n                            jax.postFilters.add(_this.sanitize.bind(jax), -5.5);\n                        }\n                    }\n                }\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                finally {\n                    try {\n                        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                    }\n                    finally { if (e_1) throw e_1.error; }\n                }\n                return _this;\n            }\n            class_1.prototype.sanitize = function (data) {\n                data.math.root = this.parseOptions.root;\n                data.document.safe.sanitize(data.math, data.document);\n            };\n            return class_1;\n        }(BaseDocument)),\n        _a.OPTIONS = __assign(__assign({}, BaseDocument.OPTIONS), { safeOptions: __assign({}, safe_js_1.Safe.OPTIONS), SafeClass: safe_js_1.Safe }),\n        _a;\n}\nexports.SafeMathDocumentMixin = SafeMathDocumentMixin;\nfunction SafeHandler(handler) {\n    handler.documentClass = SafeMathDocumentMixin(handler.documentClass);\n    return handler;\n}\nexports.SafeHandler = SafeHandler;\n//# sourceMappingURL=SafeHandler.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SafeMethods = void 0;\nvar lengths_js_1 = require(\"../../util/lengths.js\");\nexports.SafeMethods = {\n    filterURL: function (safe, url) {\n        var protocol = (url.match(/^\\s*([a-z]+):/i) || [null, ''])[1].toLowerCase();\n        var allow = safe.allow.URLs;\n        return (allow === 'all' || (allow === 'safe' &&\n            (safe.options.safeProtocols[protocol] || !protocol))) ? url : null;\n    },\n    filterClassList: function (safe, list) {\n        var _this = this;\n        var classes = list.trim().replace(/\\s\\s+/g, ' ').split(/ /);\n        return classes.map(function (name) { return _this.filterClass(safe, name) || ''; }).join(' ').trim().replace(/\\s\\s+/g, '');\n    },\n    filterClass: function (safe, CLASS) {\n        var allow = safe.allow.classes;\n        return (allow === 'all' || (allow === 'safe' && CLASS.match(safe.options.classPattern))) ? CLASS : null;\n    },\n    filterID: function (safe, id) {\n        var allow = safe.allow.cssIDs;\n        return (allow === 'all' || (allow === 'safe' && id.match(safe.options.idPattern))) ? id : null;\n    },\n    filterStyles: function (safe, styles) {\n        var e_1, _a, e_2, _b;\n        if (safe.allow.styles === 'all')\n            return styles;\n        if (safe.allow.styles !== 'safe')\n            return null;\n        var adaptor = safe.adaptor;\n        var options = safe.options;\n        try {\n            var div1 = adaptor.node('div', { style: styles });\n            var div2 = adaptor.node('div');\n            try {\n                for (var _c = __values(Object.keys(options.safeStyles)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var style = _d.value;\n                    if (options.styleParts[style]) {\n                        try {\n                            for (var _e = (e_2 = void 0, __values(['Top', 'Right', 'Bottom', 'Left'])), _f = _e.next(); !_f.done; _f = _e.next()) {\n                                var sufix = _f.value;\n                                var name_1 = style + sufix;\n                                var value = this.filterStyle(safe, name_1, div1);\n                                if (value) {\n                                    adaptor.setStyle(div2, name_1, value);\n                                }\n                            }\n                        }\n                        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                        finally {\n                            try {\n                                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                            }\n                            finally { if (e_2) throw e_2.error; }\n                        }\n                    }\n                    else {\n                        var value = this.filterStyle(safe, style, div1);\n                        if (value) {\n                            adaptor.setStyle(div2, style, value);\n                        }\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            styles = adaptor.allStyles(div2);\n        }\n        catch (err) {\n            styles = '';\n        }\n        return styles;\n    },\n    filterStyle: function (safe, style, div) {\n        var value = safe.adaptor.getStyle(div, style);\n        if (typeof value !== 'string' || value === '' || value.match(/^\\s*calc/) ||\n            (value.match(/javascript:/) && !safe.options.safeProtocols.javascript) ||\n            (value.match(/data:/) && !safe.options.safeProtocols.data)) {\n            return null;\n        }\n        var name = style.replace(/Top|Right|Left|Bottom/, '');\n        if (!safe.options.safeStyles[style] && !safe.options.safeStyles[name]) {\n            return null;\n        }\n        return this.filterStyleValue(safe, style, value, div);\n    },\n    filterStyleValue: function (safe, style, value, div) {\n        var name = safe.options.styleLengths[style];\n        if (!name) {\n            return value;\n        }\n        if (typeof name !== 'string') {\n            return this.filterStyleLength(safe, style, value);\n        }\n        var length = this.filterStyleLength(safe, name, safe.adaptor.getStyle(div, name));\n        if (!length) {\n            return null;\n        }\n        safe.adaptor.setStyle(div, name, length);\n        return safe.adaptor.getStyle(div, style);\n    },\n    filterStyleLength: function (safe, style, value) {\n        if (!value.match(/^(.+)(em|ex|ch|rem|px|mm|cm|in|pt|pc|%)$/))\n            return null;\n        var em = (0, lengths_js_1.length2em)(value, 1);\n        var lengths = safe.options.styleLengths[style];\n        var _a = __read((Array.isArray(lengths) ? lengths : [-safe.options.lengthMax, safe.options.lengthMax]), 2), m = _a[0], M = _a[1];\n        return (m <= em && em <= M ? value : (em < m ? m : M).toFixed(3).replace(/\\.?0+$/, '') + 'em');\n    },\n    filterFontSize: function (safe, size) {\n        return this.filterStyleLength(safe, 'fontSize', size);\n    },\n    filterSizeMultiplier: function (safe, size) {\n        var _a = __read(safe.options.scriptsizemultiplierRange || [-Infinity, Infinity], 2), m = _a[0], M = _a[1];\n        return Math.min(M, Math.max(m, parseFloat(size))).toString();\n    },\n    filterScriptLevel: function (safe, level) {\n        var _a = __read(safe.options.scriptlevelRange || [-Infinity, Infinity], 2), m = _a[0], M = _a[1];\n        return Math.min(M, Math.max(m, parseInt(level))).toString();\n    },\n    filterData: function (safe, value, id) {\n        return (id.match(safe.options.dataPattern) ? value : null);\n    }\n};\n//# sourceMappingURL=SafeMethods.js.map", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Safe = void 0;\nvar Options_js_1 = require(\"../../util/Options.js\");\nvar SafeMethods_js_1 = require(\"./SafeMethods.js\");\nvar Safe = (function () {\n    function Safe(document, options) {\n        this.filterAttributes = new Map([\n            ['href', 'filterURL'],\n            ['src', 'filterURL'],\n            ['altimg', 'filterURL'],\n            ['class', 'filterClassList'],\n            ['style', 'filterStyles'],\n            ['id', 'filterID'],\n            ['fontsize', 'filterFontSize'],\n            ['mathsize', 'filterFontSize'],\n            ['scriptminsize', 'filterFontSize'],\n            ['scriptsizemultiplier', 'filterSizeMultiplier'],\n            ['scriptlevel', 'filterScriptLevel'],\n            ['data-', 'filterData']\n        ]);\n        this.filterMethods = __assign({}, SafeMethods_js_1.SafeMethods);\n        this.adaptor = document.adaptor;\n        this.options = options;\n        this.allow = this.options.allow;\n    }\n    Safe.prototype.sanitize = function (math, document) {\n        try {\n            math.root.walkTree(this.sanitizeNode.bind(this));\n        }\n        catch (err) {\n            document.options.compileError(document, math, err);\n        }\n    };\n    Safe.prototype.sanitizeNode = function (node) {\n        var e_1, _a;\n        var attributes = node.attributes.getAllAttributes();\n        try {\n            for (var _b = __values(Object.keys(attributes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var id = _c.value;\n                var method = this.filterAttributes.get(id);\n                if (method) {\n                    var value = this.filterMethods[method](this, attributes[id]);\n                    if (value) {\n                        if (value !== (typeof value === 'number' ? parseFloat(attributes[id]) : attributes[id])) {\n                            attributes[id] = value;\n                        }\n                    }\n                    else {\n                        delete attributes[id];\n                    }\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    Safe.prototype.mmlAttribute = function (id, value) {\n        if (id === 'class')\n            return null;\n        var method = this.filterAttributes.get(id);\n        var filter = (method || (id.substr(0, 5) === 'data-' ? this.filterAttributes.get('data-') : null));\n        if (!filter) {\n            return value;\n        }\n        var result = this.filterMethods[filter](this, value, id);\n        return (typeof result === 'number' || typeof result === 'boolean' ? String(result) : result);\n    };\n    Safe.prototype.mmlClassList = function (list) {\n        var _this = this;\n        return list.map(function (name) { return _this.filterMethods.filterClass(_this, name); })\n            .filter(function (value) { return value !== null; });\n    };\n    Safe.OPTIONS = {\n        allow: {\n            URLs: 'safe',\n            classes: 'safe',\n            cssIDs: 'safe',\n            styles: 'safe'\n        },\n        lengthMax: 3,\n        scriptsizemultiplierRange: [.6, 1],\n        scriptlevelRange: [-2, 2],\n        classPattern: /^mjx-[-a-zA-Z0-9_.]+$/,\n        idPattern: /^mjx-[-a-zA-Z0-9_.]+$/,\n        dataPattern: /^data-mjx-/,\n        safeProtocols: (0, Options_js_1.expandable)({\n            http: true,\n            https: true,\n            file: true,\n            javascript: false,\n            data: false\n        }),\n        safeStyles: (0, Options_js_1.expandable)({\n            color: true,\n            backgroundColor: true,\n            border: true,\n            cursor: true,\n            margin: true,\n            padding: true,\n            textShadow: true,\n            fontFamily: true,\n            fontSize: true,\n            fontStyle: true,\n            fontWeight: true,\n            opacity: true,\n            outline: true\n        }),\n        styleParts: (0, Options_js_1.expandable)({\n            border: true,\n            padding: true,\n            margin: true,\n            outline: true\n        }),\n        styleLengths: (0, Options_js_1.expandable)({\n            borderTop: 'borderTopWidth',\n            borderRight: 'borderRightWidth',\n            borderBottom: 'borderBottomWidth',\n            borderLeft: 'borderLeftWidth',\n            paddingTop: true,\n            paddingRight: true,\n            paddingBottom: true,\n            paddingLeft: true,\n            marginTop: true,\n            marginRight: true,\n            marginBottom: true,\n            marginLeft: true,\n            outlineTop: true,\n            outlineRight: true,\n            outlineBottom: true,\n            outlineLeft: true,\n            fontSize: [.707, 1.44]\n        })\n    };\n    return Safe;\n}());\nexports.Safe = Safe;\n//# sourceMappingURL=safe.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.lookup = exports.separateOptions = exports.selectOptionsFromKeys = exports.selectOptions = exports.userOptions = exports.defaultOptions = exports.insert = exports.copy = exports.keys = exports.makeArray = exports.expandable = exports.Expandable = exports.OPTIONS = exports.REMOVE = exports.APPEND = exports.isObject = void 0;\nvar OBJECT = {}.constructor;\nfunction isObject(obj) {\n    return typeof obj === 'object' && obj !== null &&\n        (obj.constructor === OBJECT || obj.constructor === Expandable);\n}\nexports.isObject = isObject;\nexports.APPEND = '[+]';\nexports.REMOVE = '[-]';\nexports.OPTIONS = {\n    invalidOption: 'warn',\n    optionError: function (message, _key) {\n        if (exports.OPTIONS.invalidOption === 'fatal') {\n            throw new Error(message);\n        }\n        console.warn('MathJax: ' + message);\n    }\n};\nvar Expandable = (function () {\n    function Expandable() {\n    }\n    return Expandable;\n}());\nexports.Expandable = Expandable;\nfunction expandable(def) {\n    return Object.assign(Object.create(Expandable.prototype), def);\n}\nexports.expandable = expandable;\nfunction makeArray(x) {\n    return Array.isArray(x) ? x : [x];\n}\nexports.makeArray = makeArray;\nfunction keys(def) {\n    if (!def) {\n        return [];\n    }\n    return Object.keys(def).concat(Object.getOwnPropertySymbols(def));\n}\nexports.keys = keys;\nfunction copy(def) {\n    var e_1, _a;\n    var props = {};\n    try {\n        for (var _b = __values(keys(def)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var key = _c.value;\n            var prop = Object.getOwnPropertyDescriptor(def, key);\n            var value = prop.value;\n            if (Array.isArray(value)) {\n                prop.value = insert([], value, false);\n            }\n            else if (isObject(value)) {\n                prop.value = copy(value);\n            }\n            if (prop.enumerable) {\n                props[key] = prop;\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return Object.defineProperties(def.constructor === Expandable ? expandable({}) : {}, props);\n}\nexports.copy = copy;\nfunction insert(dst, src, warn) {\n    var e_2, _a;\n    if (warn === void 0) { warn = true; }\n    var _loop_1 = function (key) {\n        if (warn && dst[key] === undefined && dst.constructor !== Expandable) {\n            if (typeof key === 'symbol') {\n                key = key.toString();\n            }\n            exports.OPTIONS.optionError(\"Invalid option \\\"\".concat(key, \"\\\" (no default value).\"), key);\n            return \"continue\";\n        }\n        var sval = src[key], dval = dst[key];\n        if (isObject(sval) && dval !== null &&\n            (typeof dval === 'object' || typeof dval === 'function')) {\n            var ids = keys(sval);\n            if (Array.isArray(dval) &&\n                ((ids.length === 1 && (ids[0] === exports.APPEND || ids[0] === exports.REMOVE) && Array.isArray(sval[ids[0]])) ||\n                    (ids.length === 2 && ids.sort().join(',') === exports.APPEND + ',' + exports.REMOVE &&\n                        Array.isArray(sval[exports.APPEND]) && Array.isArray(sval[exports.REMOVE])))) {\n                if (sval[exports.REMOVE]) {\n                    dval = dst[key] = dval.filter(function (x) { return sval[exports.REMOVE].indexOf(x) < 0; });\n                }\n                if (sval[exports.APPEND]) {\n                    dst[key] = __spreadArray(__spreadArray([], __read(dval), false), __read(sval[exports.APPEND]), false);\n                }\n            }\n            else {\n                insert(dval, sval, warn);\n            }\n        }\n        else if (Array.isArray(sval)) {\n            dst[key] = [];\n            insert(dst[key], sval, false);\n        }\n        else if (isObject(sval)) {\n            dst[key] = copy(sval);\n        }\n        else {\n            dst[key] = sval;\n        }\n    };\n    try {\n        for (var _b = __values(keys(src)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var key = _c.value;\n            _loop_1(key);\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return dst;\n}\nexports.insert = insert;\nfunction defaultOptions(options) {\n    var defs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        defs[_i - 1] = arguments[_i];\n    }\n    defs.forEach(function (def) { return insert(options, def, false); });\n    return options;\n}\nexports.defaultOptions = defaultOptions;\nfunction userOptions(options) {\n    var defs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        defs[_i - 1] = arguments[_i];\n    }\n    defs.forEach(function (def) { return insert(options, def, true); });\n    return options;\n}\nexports.userOptions = userOptions;\nfunction selectOptions(options) {\n    var e_3, _a;\n    var keys = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        keys[_i - 1] = arguments[_i];\n    }\n    var subset = {};\n    try {\n        for (var keys_1 = __values(keys), keys_1_1 = keys_1.next(); !keys_1_1.done; keys_1_1 = keys_1.next()) {\n            var key = keys_1_1.value;\n            if (options.hasOwnProperty(key)) {\n                subset[key] = options[key];\n            }\n        }\n    }\n    catch (e_3_1) { e_3 = { error: e_3_1 }; }\n    finally {\n        try {\n            if (keys_1_1 && !keys_1_1.done && (_a = keys_1.return)) _a.call(keys_1);\n        }\n        finally { if (e_3) throw e_3.error; }\n    }\n    return subset;\n}\nexports.selectOptions = selectOptions;\nfunction selectOptionsFromKeys(options, object) {\n    return selectOptions.apply(void 0, __spreadArray([options], __read(Object.keys(object)), false));\n}\nexports.selectOptionsFromKeys = selectOptionsFromKeys;\nfunction separateOptions(options) {\n    var e_4, _a, e_5, _b;\n    var objects = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        objects[_i - 1] = arguments[_i];\n    }\n    var results = [];\n    try {\n        for (var objects_1 = __values(objects), objects_1_1 = objects_1.next(); !objects_1_1.done; objects_1_1 = objects_1.next()) {\n            var object = objects_1_1.value;\n            var exists = {}, missing = {};\n            try {\n                for (var _c = (e_5 = void 0, __values(Object.keys(options || {}))), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var key = _d.value;\n                    (object[key] === undefined ? missing : exists)[key] = options[key];\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            results.push(exists);\n            options = missing;\n        }\n    }\n    catch (e_4_1) { e_4 = { error: e_4_1 }; }\n    finally {\n        try {\n            if (objects_1_1 && !objects_1_1.done && (_a = objects_1.return)) _a.call(objects_1);\n        }\n        finally { if (e_4) throw e_4.error; }\n    }\n    results.unshift(options);\n    return results;\n}\nexports.separateOptions = separateOptions;\nfunction lookup(name, lookup, def) {\n    if (def === void 0) { def = null; }\n    return (lookup.hasOwnProperty(name) ? lookup[name] : def);\n}\nexports.lookup = lookup;\n//# sourceMappingURL=Options.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.px = exports.emRounded = exports.em = exports.percent = exports.length2em = exports.MATHSPACE = exports.RELUNITS = exports.UNITS = exports.BIGDIMEN = void 0;\nexports.BIGDIMEN = 1000000;\nexports.UNITS = {\n    px: 1,\n    'in': 96,\n    cm: 96 / 2.54,\n    mm: 96 / 25.4\n};\nexports.RELUNITS = {\n    em: 1,\n    ex: .431,\n    pt: 1 / 10,\n    pc: 12 / 10,\n    mu: 1 / 18\n};\nexports.MATHSPACE = {\n    veryverythinmathspace: 1 / 18,\n    verythinmathspace: 2 / 18,\n    thinmathspace: 3 / 18,\n    mediummathspace: 4 / 18,\n    thickmathspace: 5 / 18,\n    verythickmathspace: 6 / 18,\n    veryverythickmathspace: 7 / 18,\n    negativeveryverythinmathspace: -1 / 18,\n    negativeverythinmathspace: -2 / 18,\n    negativethinmathspace: -3 / 18,\n    negativemediummathspace: -4 / 18,\n    negativethickmathspace: -5 / 18,\n    negativeverythickmathspace: -6 / 18,\n    negativeveryverythickmathspace: -7 / 18,\n    thin: .04,\n    medium: .06,\n    thick: .1,\n    normal: 1,\n    big: 2,\n    small: 1 / Math.sqrt(2),\n    infinity: exports.BIGDIMEN\n};\nfunction length2em(length, size, scale, em) {\n    if (size === void 0) { size = 0; }\n    if (scale === void 0) { scale = 1; }\n    if (em === void 0) { em = 16; }\n    if (typeof length !== 'string') {\n        length = String(length);\n    }\n    if (length === '' || length == null) {\n        return size;\n    }\n    if (exports.MATHSPACE[length]) {\n        return exports.MATHSPACE[length];\n    }\n    var match = length.match(/^\\s*([-+]?(?:\\.\\d+|\\d+(?:\\.\\d*)?))?(pt|em|ex|mu|px|pc|in|mm|cm|%)?/);\n    if (!match) {\n        return size;\n    }\n    var m = parseFloat(match[1] || '1'), unit = match[2];\n    if (exports.UNITS.hasOwnProperty(unit)) {\n        return m * exports.UNITS[unit] / em / scale;\n    }\n    if (exports.RELUNITS.hasOwnProperty(unit)) {\n        return m * exports.RELUNITS[unit];\n    }\n    if (unit === '%') {\n        return m / 100 * size;\n    }\n    return m * size;\n}\nexports.length2em = length2em;\nfunction percent(m) {\n    return (100 * m).toFixed(1).replace(/\\.?0+$/, '') + '%';\n}\nexports.percent = percent;\nfunction em(m) {\n    if (Math.abs(m) < .001)\n        return '0';\n    return (m.toFixed(3).replace(/\\.?0+$/, '')) + 'em';\n}\nexports.em = em;\nfunction emRounded(m, em) {\n    if (em === void 0) { em = 16; }\n    m = (Math.round(m * em) + .05) / em;\n    if (Math.abs(m) < .001)\n        return '0em';\n    return m.toFixed(3).replace(/\\.?0+$/, '') + 'em';\n}\nexports.emRounded = emRounded;\nfunction px(m, M, em) {\n    if (M === void 0) { M = -exports.BIGDIMEN; }\n    if (em === void 0) { em = 16; }\n    m *= em;\n    if (M && m < M)\n        m = M;\n    if (Math.abs(m) < .1)\n        return '0';\n    return m.toFixed(1).replace(/\\.0$/, '') + 'px';\n}\nexports.px = px;\n//# sourceMappingURL=lengths.js.map"], "names": [], "sourceRoot": ""}
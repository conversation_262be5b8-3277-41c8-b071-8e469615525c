# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, learning_curve
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")
np.random.seed(42)

# Load the dataset
df = pd.read_csv('worker_productivity.csv')

print(f"Dataset shape: {df.shape}")
print(f"\nColumns: {list(df.columns)}")
print(f"\nFirst few rows:")
df.head()

# Check data types and missing values
print("Data Info:")
print(df.info())
print("\nMissing values:")
print(df.isnull().sum())
print("\nBasic statistics:")
df.describe()

# Create comprehensive visualizations
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Worker Productivity Data Analysis', fontsize=16, fontweight='bold')

# 1. Target variable distribution
axes[0, 0].hist(df['actual_productivity'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
axes[0, 0].set_title('Distribution of Actual Productivity')
axes[0, 0].set_xlabel('Actual Productivity')
axes[0, 0].set_ylabel('Frequency')

# 2. Correlation heatmap for numerical features
numeric_cols = df.select_dtypes(include=[np.number]).columns
corr_matrix = df[numeric_cols].corr()
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
           square=True, ax=axes[0, 1], fmt='.2f', cbar_kws={'shrink': 0.8})
axes[0, 1].set_title('Correlation Matrix')

# 3. Productivity by department
dept_prod = df.groupby('department')['actual_productivity'].mean().sort_values()
axes[0, 2].bar(range(len(dept_prod)), dept_prod.values, color='lightcoral')
axes[0, 2].set_title('Average Productivity by Department')
axes[0, 2].set_xlabel('Department')
axes[0, 2].set_ylabel('Average Productivity')
axes[0, 2].set_xticks(range(len(dept_prod)))
axes[0, 2].set_xticklabels(dept_prod.index, rotation=45)

# 4. Actual vs Targeted productivity
axes[1, 0].scatter(df['targeted_productivity'], df['actual_productivity'], 
                  alpha=0.6, color='green')
axes[1, 0].set_title('Actual vs Targeted Productivity')
axes[1, 0].set_xlabel('Targeted Productivity')
axes[1, 0].set_ylabel('Actual Productivity')
axes[1, 0].plot([0, 1], [0, 1], 'r--', alpha=0.8)

# 5. Missing data visualization
missing_data = df.isnull().sum()
missing_data = missing_data[missing_data > 0]
if len(missing_data) > 0:
    axes[1, 1].bar(range(len(missing_data)), missing_data.values, color='orange')
    axes[1, 1].set_title('Missing Data by Column')
    axes[1, 1].set_xlabel('Columns')
    axes[1, 1].set_ylabel('Missing Count')
    axes[1, 1].set_xticks(range(len(missing_data)))
    axes[1, 1].set_xticklabels(missing_data.index, rotation=45)
else:
    axes[1, 1].text(0.5, 0.5, 'No Missing Data', ha='center', va='center', 
                   transform=axes[1, 1].transAxes, fontsize=14)
    axes[1, 1].set_title('Missing Data Analysis')

# 6. Productivity by day of week
day_prod = df.groupby('day')['actual_productivity'].mean().sort_values()
axes[1, 2].bar(range(len(day_prod)), day_prod.values, color='purple', alpha=0.7)
axes[1, 2].set_title('Average Productivity by Day')
axes[1, 2].set_xlabel('Day of Week')
axes[1, 2].set_ylabel('Average Productivity')
axes[1, 2].set_xticks(range(len(day_prod)))
axes[1, 2].set_xticklabels(day_prod.index, rotation=45)

plt.tight_layout()
plt.show()

# Create a copy for processing
df_processed = df.copy()

# Handle missing values in 'wip' column
print(f"Missing values in 'wip': {df_processed['wip'].isnull().sum()}")

# Check which departments have missing WIP values
missing_wip = df_processed['wip'].isnull()
print(f"Departments with missing WIP:")
print(df_processed[missing_wip]['department'].value_counts())

# Use median imputation by department for WIP
df_processed['wip'] = df_processed.groupby('department')['wip'].transform(
    lambda x: x.fillna(x.median())
)

# If still missing, use overall median
df_processed['wip'] = df_processed['wip'].fillna(df_processed['wip'].median())

print(f"Missing values after imputation: {df_processed['wip'].isnull().sum()}")

# Feature Engineering
print("Creating new features...")

# Parse date and extract temporal features
df_processed['date'] = pd.to_datetime(df_processed['date'])
df_processed['month'] = df_processed['date'].dt.month
df_processed['day_of_month'] = df_processed['date'].dt.day

# Create per-worker metrics
df_processed['overtime_per_worker'] = df_processed['over_time'] / df_processed['no_of_workers']
df_processed['incentive_per_worker'] = df_processed['incentive'] / df_processed['no_of_workers']
df_processed['idle_ratio'] = df_processed['idle_men'] / df_processed['no_of_workers']

# Clean department names (remove trailing spaces)
df_processed['department'] = df_processed['department'].str.strip()

print("New features created:")
print("- overtime_per_worker: Overtime hours per worker")
print("- incentive_per_worker: Incentive amount per worker")
print("- idle_ratio: Proportion of idle workers")
print("- month, day_of_month: Temporal features")

# Encode categorical variables
categorical_cols = ['quarter', 'department', 'day']
df_encoded = pd.get_dummies(df_processed, columns=categorical_cols, prefix=categorical_cols)

# Select features for modeling (exclude target and non-predictive columns)
feature_cols = [col for col in df_encoded.columns if col not in 
               ['actual_productivity', 'date']]

X = df_encoded[feature_cols]
y = df_encoded['actual_productivity']

print(f"Final feature set: {len(feature_cols)} features")
print(f"Features: {feature_cols[:10]}...")  # Show first 10 features

# Split the data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

# Scale features for algorithms that need it
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print(f"\nData split:")
print(f"Training set: {X_train.shape}")
print(f"Test set: {X_test.shape}")

# Define models to evaluate
models = {
    'Linear Regression': LinearRegression(),
    'Ridge Regression': Ridge(alpha=1.0),
    'Lasso Regression': Lasso(alpha=0.1),
    'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
    'SVR': SVR(kernel='rbf', C=1.0),
    'KNN': KNeighborsRegressor(n_neighbors=5)
}

# Store results
results = {}

print("Evaluating different algorithms...\n")

for name, model in models.items():
    print(f"Training {name}...")
    
    # Use scaled data for models that need it
    if name in ['SVR', 'KNN', 'Linear Regression', 'Ridge Regression', 'Lasso Regression']:
        X_train_use = X_train_scaled
        X_test_use = X_test_scaled
    else:
        X_train_use = X_train
        X_test_use = X_test
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_train_use, y_train, 
                              cv=5, scoring='neg_mean_squared_error')
    cv_rmse = np.sqrt(-cv_scores)
    
    # Fit model and make predictions
    model.fit(X_train_use, y_train)
    y_pred = model.predict(X_test_use)
    
    # Calculate metrics
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    # Store results
    results[name] = {
        'CV_RMSE_mean': cv_rmse.mean(),
        'CV_RMSE_std': cv_rmse.std(),
        'Test_RMSE': rmse,
        'Test_MAE': mae,
        'Test_R2': r2,
        'Model': model
    }
    
    print(f"  CV RMSE: {cv_rmse.mean():.4f} (+/- {cv_rmse.std() * 2:.4f})")
    print(f"  Test RMSE: {rmse:.4f}")
    print(f"  Test R²: {r2:.4f}\n")

print("Model evaluation completed!")

# Create results summary table
summary_data = []
for name, result in results.items():
    summary_data.append({
        'Model': name,
        'CV_RMSE': result['CV_RMSE_mean'],
        'Test_RMSE': result['Test_RMSE'],
        'Test_MAE': result['Test_MAE'],
        'Test_R²': result['Test_R2']
    })

results_df = pd.DataFrame(summary_data).sort_values('Test_RMSE')
print("Model Performance Summary (sorted by Test RMSE):")
print(results_df.round(4))

# Get top 3 models for tuning
top_models = results_df.head(3)['Model'].tolist()
print(f"Tuning hyperparameters for top 3 models: {top_models}")

tuned_results = {}

# Random Forest tuning
if 'Random Forest' in top_models:
    print("\nTuning Random Forest...")
    rf_params = {
        'n_estimators': [50, 100, 200],
        'max_depth': [10, 20, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    }
    rf_grid = GridSearchCV(RandomForestRegressor(random_state=42), rf_params, 
                         cv=3, scoring='neg_mean_squared_error', n_jobs=-1)
    rf_grid.fit(X_train, y_train)
    
    y_pred_rf = rf_grid.predict(X_test)
    tuned_results['Random Forest'] = {
        'best_params': rf_grid.best_params_,
        'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_rf)),
        'test_r2': r2_score(y_test, y_pred_rf)
    }
    print(f"Best params: {rf_grid.best_params_}")
    print(f"Test RMSE: {tuned_results['Random Forest']['test_rmse']:.4f}")

# Gradient Boosting tuning
if 'Gradient Boosting' in top_models:
    print("\nTuning Gradient Boosting...")
    gb_params = {
        'n_estimators': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 5, 7]
    }
    gb_grid = GridSearchCV(GradientBoostingRegressor(random_state=42), gb_params, 
                         cv=3, scoring='neg_mean_squared_error', n_jobs=-1)
    gb_grid.fit(X_train, y_train)
    
    y_pred_gb = gb_grid.predict(X_test)
    tuned_results['Gradient Boosting'] = {
        'best_params': gb_grid.best_params_,
        'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_gb)),
        'test_r2': r2_score(y_test, y_pred_gb)
    }
    print(f"Best params: {gb_grid.best_params_}")
    print(f"Test RMSE: {tuned_results['Gradient Boosting']['test_rmse']:.4f}")

# Select best model for learning curve analysis
best_model_name = results_df.iloc[0]['Model']
best_model = results[best_model_name]['Model']

print(f"Analyzing learning curves for: {best_model_name}")

# Determine if we need scaled data
if best_model_name in ['SVR', 'KNN', 'Linear Regression', 'Ridge Regression', 'Lasso Regression']:
    X_use = X_train_scaled
else:
    X_use = X_train

# Generate learning curves
train_sizes, train_scores, val_scores = learning_curve(
    best_model, X_use, y_train, cv=5, 
    train_sizes=np.linspace(0.1, 1.0, 10),
    scoring='neg_mean_squared_error', n_jobs=-1
)

# Convert to RMSE
train_rmse = np.sqrt(-train_scores)
val_rmse = np.sqrt(-val_scores)

# Plot learning curves
plt.figure(figsize=(10, 6))
plt.plot(train_sizes, train_rmse.mean(axis=1), 'o-', color='blue', label='Training RMSE')
plt.fill_between(train_sizes, train_rmse.mean(axis=1) - train_rmse.std(axis=1),
                train_rmse.mean(axis=1) + train_rmse.std(axis=1), alpha=0.1, color='blue')

plt.plot(train_sizes, val_rmse.mean(axis=1), 'o-', color='red', label='Validation RMSE')
plt.fill_between(train_sizes, val_rmse.mean(axis=1) - val_rmse.std(axis=1),
                val_rmse.mean(axis=1) + val_rmse.std(axis=1), alpha=0.1, color='red')

plt.xlabel('Training Set Size')
plt.ylabel('RMSE')
plt.title(f'Learning Curves - {best_model_name}')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Analyze overfitting/underfitting
final_train_rmse = train_rmse.mean(axis=1)[-1]
final_val_rmse = val_rmse.mean(axis=1)[-1]
gap = final_val_rmse - final_train_rmse

print(f"\nLearning Curve Analysis:")
print(f"Final Training RMSE: {final_train_rmse:.4f}")
print(f"Final Validation RMSE: {final_val_rmse:.4f}")
print(f"Gap (Val - Train): {gap:.4f}")

if gap > 0.05:
    print("⚠️  Potential overfitting detected (large gap between train and validation)")
elif final_val_rmse > 0.15:
    print("⚠️  Potential underfitting detected (high validation error)")
else:
    print("✅ Model appears to have good bias-variance tradeoff")

# Define different neural network architectures
nn_configs = {
    'Small NN': MLPRegressor(hidden_layer_sizes=(50,), max_iter=1000, random_state=42),
    'Medium NN': MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42),
    'Large NN': MLPRegressor(hidden_layer_sizes=(200, 100, 50), max_iter=1000, random_state=42),
    'Deep NN': MLPRegressor(hidden_layer_sizes=(100, 100, 100), max_iter=1000, random_state=42)
}

nn_results = {}

print("Training Neural Networks...\n")

for name, model in nn_configs.items():
    print(f"Training {name}...")
    
    # Neural networks need scaled data
    model.fit(X_train_scaled, y_train)
    y_pred = model.predict(X_test_scaled)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_train_scaled, y_train, 
                              cv=5, scoring='neg_mean_squared_error')
    cv_rmse = np.sqrt(-cv_scores)
    
    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    r2 = r2_score(y_test, y_pred)
    
    nn_results[name] = {
        'CV_RMSE_mean': cv_rmse.mean(),
        'Test_RMSE': rmse,
        'Test_R2': r2,
        'Architecture': model.hidden_layer_sizes
    }
    
    print(f"  Architecture: {model.hidden_layer_sizes}")
    print(f"  Test RMSE: {rmse:.4f}")
    print(f"  Test R²: {r2:.4f}\n")

# Compare with best traditional ML model
best_ml_model = results_df.iloc[0]['Model']
best_ml_rmse = results_df.iloc[0]['Test_RMSE']
best_ml_r2 = results_df.iloc[0]['Test_R²']

best_nn_model = min(nn_results.keys(), key=lambda x: nn_results[x]['Test_RMSE'])
best_nn_rmse = nn_results[best_nn_model]['Test_RMSE']
best_nn_r2 = nn_results[best_nn_model]['Test_R2']

print("=" * 50)
print("NEURAL NETWORK vs TRADITIONAL ML COMPARISON")
print("=" * 50)
print(f"Best Traditional ML: {best_ml_model}")
print(f"  Test RMSE: {best_ml_rmse:.4f}")
print(f"  Test R²: {best_ml_r2:.4f}")

print(f"\nBest Neural Network: {best_nn_model}")
print(f"  Test RMSE: {best_nn_rmse:.4f}")
print(f"  Test R²: {best_nn_r2:.4f}")

# Determine winner
if best_nn_rmse < best_ml_rmse:
    print(f"\n🏆 Neural Network ({best_nn_model}) performs better!")
else:
    improvement = ((best_nn_rmse - best_ml_rmse) / best_ml_rmse) * 100
    print(f"\n🏆 Traditional ML ({best_ml_model}) performs better by {improvement:.1f}%!")

# Get feature importance from Random Forest (if it's one of our models)
if 'Random Forest' in results:
    rf_model = results['Random Forest']['Model']
    feature_names = X_train.columns
    importances = rf_model.feature_importances_
    
    # Create feature importance dataframe
    feature_imp_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importances
    }).sort_values('importance', ascending=False)
    
    print("Top 10 Most Important Features:")
    print(feature_imp_df.head(10))
    
    # Plot feature importance
    plt.figure(figsize=(12, 8))
    top_features = feature_imp_df.head(15)
    plt.barh(range(len(top_features)), top_features['importance'], color='skyblue')
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('Feature Importance')
    plt.title('Top 15 Feature Importances (Random Forest)')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.show()
else:
    print("Random Forest not available for feature importance analysis.")

# Get predictions from best model
best_model_name = results_df.iloc[0]['Model']
best_model = results[best_model_name]['Model']

# Make predictions
if best_model_name in ['SVR', 'KNN', 'Linear Regression', 'Ridge Regression', 'Lasso Regression']:
    y_pred_best = best_model.predict(X_test_scaled)
else:
    y_pred_best = best_model.predict(X_test)

# Calculate final metrics
final_rmse = np.sqrt(mean_squared_error(y_test, y_pred_best))
final_mae = mean_absolute_error(y_test, y_pred_best)
final_r2 = r2_score(y_test, y_pred_best)

# Create prediction vs actual plot
plt.figure(figsize=(10, 8))
plt.scatter(y_test, y_pred_best, alpha=0.6, color='blue', s=50)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
plt.xlabel('Actual Productivity')
plt.ylabel('Predicted Productivity')
plt.title(f'{best_model_name}: Predicted vs Actual Productivity')
plt.grid(True, alpha=0.3)

# Add performance metrics to plot
plt.text(0.05, 0.95, f'RMSE: {final_rmse:.4f}\nMAE: {final_mae:.4f}\nR²: {final_r2:.4f}', 
         transform=plt.gca().transAxes, verticalalignment='top',
         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

plt.tight_layout()
plt.show()

# Print final summary
print("=" * 60)
print("FINAL MODEL PERFORMANCE SUMMARY")
print("=" * 60)
print(f"Best Model: {best_model_name}")
print(f"Test RMSE: {final_rmse:.4f}")
print(f"Test MAE: {final_mae:.4f}")
print(f"Test R²: {final_r2:.4f}")

# Business interpretation
mean_productivity = y_train.mean()
rmse_percentage = (final_rmse / mean_productivity) * 100

print(f"\nBusiness Impact:")
print(f"- Average productivity: {mean_productivity:.3f}")
print(f"- Prediction accuracy: ±{rmse_percentage:.1f}% of actual productivity")
print(f"- Model explains {final_r2*100:.1f}% of productivity variance")

if final_r2 > 0.7:
    print(f"- Model performance: Excellent - suitable for production use")
elif final_r2 > 0.5:
    print(f"- Model performance: Good - can provide valuable insights")
elif final_r2 > 0.3:
    print(f"- Model performance: Moderate - useful for trend analysis")
else:
    print(f"- Model performance: Needs improvement - consider more features")